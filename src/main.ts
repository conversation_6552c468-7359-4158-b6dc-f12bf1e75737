import '@vidmob/vidmob-nestjs-common/dist/tracing';

import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import {
  DEFAULT_PORT,
  VidmobCommonModule,
  RookoutService,
} from '@vidmob/vidmob-nestjs-common';
import { VersioningType } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  CONFIG_CORS_ORIGIN,
  CONFIG_ENV_LOCAL,
  CONFIG_ENV_SDK,
  CONFIG_PORT,
  CORS_ALLOW_HEADERS,
} from './constants/configuration.constants';
import {
  API_DESCRIPTION,
  API_TITLE,
  API_VERSION,
  SERVICE_NAME,
  STRICT_TRANSPORT_SECURITY_MAX_AGE,
  URL_ENCODED_BODY_SIZE_LIMIT,
  URL_ENCODED_DEFAULT_BODY_SIZE_LIMIT,
} from './constants/api.constants';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { urlencoded } from 'express';
import { CorsOptions } from '@nestjs/common/interfaces/external/cors-options.interface';
import helmet from 'helmet';

const SWAGGER_URI = 'docs';
const CORS_ALLOW_ORIGIN_ALL = '*';
const CORS_DEFAULT_ALLOW_HEADERS = ['Content-Type', 'Authorization'];

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  VidmobCommonModule.setupLogger(app); // Setup logger (uses Pino Logger)

  app.enableVersioning({
    type: VersioningType.URI,
    defaultVersion: '1',
  });
  const configService = app.get(ConfigService);
  const environment = configService.get('NODE_ENV');
  const rookout = app.get(RookoutService);
  await rookout.init(environment, { service: SERVICE_NAME });

  if (environment === CONFIG_ENV_SDK || environment === CONFIG_ENV_LOCAL) {
    const port = configService.get<number>(CONFIG_PORT, DEFAULT_PORT);
    const config = new DocumentBuilder()
      .setTitle(API_TITLE)
      .setDescription(API_DESCRIPTION)
      .setVersion(API_VERSION)
      .addServer('https://bff-dev.vidmob.com', 'Dev')
      .addServer('https://bff-stage.vidmob.com', 'Stage')
      .addServer('https://bff.vidmob.com', 'Prod')
      .addServer(`http://localhost:${port}`, 'Local')
      .addBearerAuth(
        {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'Bearer Token',
          name: 'Authorization',
          description:
            'Enter your Bearer Token or API Key (without "Bearer " prefix)',
          in: 'header',
        },
        'Bearer Token', // This is the key that will be used to reference this auth method
      )
      .build();
    const document = SwaggerModule.createDocument(app, config);
    SwaggerModule.setup(SWAGGER_URI, app, document);
  }

  const port = configService.get<number>(CONFIG_PORT, DEFAULT_PORT);
  console.log('Listening on port: ' + port);
  const allowList = configService.get<string[]>(CONFIG_CORS_ORIGIN, [
    CORS_ALLOW_ORIGIN_ALL,
  ]);
  const allowHeaderList = configService.get<string[]>(
    CORS_ALLOW_HEADERS,
    CORS_DEFAULT_ALLOW_HEADERS,
  );

  /*
   * This is a custom CORS implementation that allows us to use a whitelist
   */
  app.enableCors((req: any, callback: any) => {
    let corsOptions = { allowedHeaders: allowHeaderList } as CorsOptions;
    const originHeader = req.header('Origin');

    if (allowList.indexOf(CORS_ALLOW_ORIGIN_ALL) !== -1) {
      return callback(null, { origin: CORS_ALLOW_ORIGIN_ALL });
    }

    if (allowList.indexOf(originHeader) !== -1) {
      corsOptions = { origin: originHeader, ...corsOptions };
    } else {
      corsOptions = { origin: undefined, ...corsOptions };
    }
    callback(null, corsOptions);
  });

  app.use(
    '/notifications/mailchimp/webhook',
    urlencoded({ extended: true, limit: URL_ENCODED_BODY_SIZE_LIMIT }),
  );
  // resetting the limit for all other routes
  app.use(
    urlencoded({ extended: true, limit: URL_ENCODED_DEFAULT_BODY_SIZE_LIMIT }),
  );

  app.use(
    helmet({
      strictTransportSecurity: {
        maxAge: STRICT_TRANSPORT_SECURITY_MAX_AGE,
      },
    }),
  );

  await app.listen(port);
}
bootstrap();
