import { Body, Controller, Logger, Post, Req } from '@nestjs/common';
import { ApiSecurity, ApiTags } from '@nestjs/swagger';
import { OAuth2Service } from './oauth2.service';
import { VmApiOkResponse } from '@vidmob/vidmob-nestjs-common';
import {
  AccessTokenRequestDto,
  RevokeTokenRequestDto,
} from '@vidmob/vidmob-authorization-service-sdk';
import { Public } from 'src/auth/decorators/public.decorator';
import { Request } from 'express';
import { AccessTokenResponseDto } from './dto/access-token-response.dto';
import { RevokeTokenResponseDto } from './dto/revoke-token-response.dto';

@ApiTags('OAuth2')
@Controller('oauth2')
export class OAuth2Controller {
  private readonly logger = new Logger(OAuth2Controller.name);
  constructor(private readonly oauth2Service: OAuth2Service) {}

  /**
   * It generates a new access token based on the code and grant type
   * @param accessTokenRequestDto
   */
  @VmApiOkResponse({
    description: 'Creates an access token',
    type: [AccessTokenResponseDto],
  })
  @Post('token')
  @Public() // public since the authorization might be expired and this is a request to generate a new one
  async createAccessToken(
    @Body() accessTokenRequestDto: AccessTokenRequestDto,
  ) {
    return await this.oauth2Service.createAccessToken(accessTokenRequestDto);
  }

  /**
   * It revokes the token for the user and application.
   * If this token is a cognito token send a revoke token command to cognito.
   * If this token is a refresh token, revoke all tokens this user has on this application.
   * @param revokeTokenRequestDto
   */
  @VmApiOkResponse({
    description: 'Revoke token message',
    type: [RevokeTokenResponseDto],
  })
  @Post('revoke')
  async revokeToken(
    @Req() req: Request,
    @Body() revokeTokenRequestDto: RevokeTokenRequestDto,
  ) {
    const { authorization } = req.headers;

    return await this.oauth2Service.revokeToken(
      authorization as string,
      revokeTokenRequestDto,
    );
  }
}
