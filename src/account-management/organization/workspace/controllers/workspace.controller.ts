import {
  Controller,
  Get,
  Logger,
  Param,
  Request,
  Query,
  Patch,
  ValidationPipe,
  Body,
  Post,
  UnauthorizedException,
} from '@nestjs/common';
import { ApiBody, ApiParam, ApiQuery, ApiSecurity, ApiTags } from '@nestjs/swagger';
import { WorkspaceService } from '../services/workspace.service';
import { SearchParamsDto } from '../dto/search-params.dto';
import {
  GetPagination,
  PaginationOptions,
  VmApiOkResponse,
} from '@vidmob/vidmob-nestjs-common';
import {
  readWorkspaces,
  updateWorkspaceDetails,
  createWorkspace,
  readAllWorkspaces,
} from '../../../account-management.permissions';
import { Permissions } from '../../../../auth/decorators/permission.decorator';
import { UpdateWorkspaceDto } from '../dto/update-workspace.dto';
import { UUID } from 'crypto';
import { CreateWorkspaceDto } from '../dto/create-workspace.dto';
import { AdAccountService } from '../../ad-account/services/ad-account.service';
import { AccountSearchParamsDto } from '../../ad-account/entities/ad-account-search-params.dto';
import { AuthService } from '../../../../auth/services/auth.service';
import { CreateAdAccountBrandMapDto } from '../dto/create-ad-account-brand-map.dto';
import { ReadAdAccountMapDto } from '../dto/read-ad-account-map.dto';
import { CreateAdAccountMarketMapDto } from '../dto/create-ad-account-market-map.dto';
import { ReadAdAccountBrandsDto } from '../dto/read-ad-account-brands.dto';

@ApiTags('Workspace')
@ApiSecurity('Bearer Token')
@Controller('account-management/organization/:organizationId/workspace')
export class WorkspaceController {
  private readonly logger = new Logger(WorkspaceService.name);

  constructor(
    private readonly workspaceService: WorkspaceService,
    private readonly adAccountService: AdAccountService,
    private readonly authService: AuthService,
  ) {}

  /**
   * Lightweight endpoint to load all workspaces within an organization.
   * The purpose of this endpoint is to provide a fast query to retrieve all workspaces, without much querying.
   * @param organizationId - The organization ID represents the unique identifier of the organization to which all workspaces belong.
   * @returns - all organization workspace: name and id
   */
  @ApiParam({
    name: 'organizationId',
    description:
      'The organization ID represents the unique identifier of the organization to which all workspaces belong.',
  })
  @Permissions(readAllWorkspaces)
  @Get('all')
  async getAllWorkspaces(@Param('organizationId') organizationId: string) {
    return await this.workspaceService.getAllLightweight(organizationId);
  }

  /**
   * Get all workspaces by organization and market.
   * @param organizationId - The organization ID represents the unique identifier of the organization to which all workspaces belong.
   * @param market - The market (Country) isoCode to filter workspaces.
   * @param brand - The brand name to filter workspaces.
   * @param search - The search param to filter workspaces by name.
   * @param offset - Determines the index of the first workspace to return.
   * @param perPage - Determines the number of workspaces to return.
   * @returns - The workspaces filtered by organization and market.
   */
  @ApiParam({
    name: 'organizationId',
    description:
      'The organization ID represents the unique identifier of the organization to which all workspaces belong.',
  })
  @ApiQuery({
    name: 'market',
    description: 'The market (Country) isoCode to filter workspaces.',
    example: '?market=bra,usa,fra',
  })
  @ApiQuery({
    name: 'brand',
    description: 'The brand name to filter workspaces.',
    example: '?brand=VidMob cool brand',
  })
  @ApiQuery({
    name: 'search',
    description: 'The search param to filter workspaces by name.',
    example: '?search=my cool Workspace',
  })
  @ApiQuery({
    name: 'offset',
    description: 'Determines the index of the first workspace to return.',
  })
  @ApiQuery({
    name: 'perPage',
    description: 'Determines the number of workspaces to return.',
  })
  @Permissions(readWorkspaces)
  @Get()
  async getWorkspacesByOrganizationIdAndSearch(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Query() searchParams: SearchParamsDto,
    @GetPagination() paginationOptions: PaginationOptions,
  ) {
    const { userId } = req;
    const authorization = req.headers.authorization;

    return await this.workspaceService.getWorkspacesByOrganizationId(
      organizationId,
      userId,
      authorization,
      paginationOptions,
      searchParams,
    );
  }

  /**
   * Show a workspace by id
   * @param workspaceId - The workspace ID represents the unique identifier of the workspace to be retrieved.
   */
  @ApiParam({
    name: 'workspaceId',
    description:
      'The workspace ID represents the unique id of the workspace to be retrieved.',
  })
  @Permissions(readWorkspaces)
  @Get(':workspaceId')
  async showWorkspaceById(@Param('workspaceId') workspaceId: number) {
    return await this.workspaceService.getWorkspaceById(workspaceId);
  }

  /**
   * Create a new workspace
   *
   * @param createWorkspaceDto
   * @returns
   */
  @ApiParam({
    name: 'organizationId',
    description:
      'The unique identifier of the organization for which the new workspace will be created (provided as a URL parameter).',
  })
  @ApiBody({
    type: CreateWorkspaceDto,
  })
  @Permissions(createWorkspace)
  @Post()
  async createWorkspace(
    @Param('organizationId') organizationId: UUID,
    @Body(new ValidationPipe()) createWorkspaceDto: CreateWorkspaceDto,
  ) {
    return this.workspaceService.createWorkspace(
      organizationId,
      createWorkspaceDto,
    );
  }

  /**
   * Update a workspace by id
   *
   * @param id
   * @param updateWorkspaceDto
   * @returns
   */
  @ApiParam({
    name: 'organizationId',
    description:
      'The organization ID represents the unique identifier of the organization to which all workspaces belong.',
  })
  @Permissions(updateWorkspaceDetails)
  @Patch(':workspaceId')
  updateWorkspace(
    @Param('organizationId') organizationId: UUID,
    @Param('workspaceId') workspaceId: number,
    @Body(new ValidationPipe()) updateWorkspaceDto: UpdateWorkspaceDto,
  ) {
    return this.workspaceService.updateWorkspace(
      organizationId,
      workspaceId,
      updateWorkspaceDto,
    );
  }

  /**
   * Get Ad Accounts list for health dashboard.
   * @param req - Request attributes
   * @param organizationId - Organization ID
   * @param workspaceId - Workspace ID
   * @param searchParams - Search param to filter
   * @param paginationOptions - Pagination options
   */
  @ApiParam({
    name: 'organizationId',
    description:
      'The organization ID represents the unique identifier of the organization.',
  })
  @ApiParam({
    name: 'workspaceId',
    description:
      'The workspace ID represents the unique identifier of the partner.',
  })
  @ApiQuery({
    name: 'search',
    description:
      'The search param to filter ad accounts by name, id or channel.',
    example: '?search=my cool Workspace',
  })
  @ApiQuery({
    name: 'offset',
    description: 'Determines the index of the first ad account to return.',
  })
  @ApiQuery({
    name: 'perPage',
    description: 'Determines the number of ad accounts to return.',
  })
  @ApiQuery({
    name: 'sortBy',
    description: 'The sort by string to determine the field to sort results.',
    example: '?sortBy=channel - platform_account_name (default)',
  })
  @ApiQuery({
    name: 'sortOrder',
    description: 'The sort by order.',
    example: '?sortOrder=ASC - ASC(default) or DESC.',
  })
  @Get(':workspaceId/ad-account-health')
  async getAdAccountsListForHealthDashboard(
    @Request() req: any,
    @Param('organizationId') organizationId: UUID,
    @Param('workspaceId') workspaceId: number,
    @Query(new ValidationPipe({ transform: true }))
    searchParams: AccountSearchParamsDto,
    @GetPagination() paginationOptions: PaginationOptions,
  ) {
    const { userId } = req;
    const authorization = req.headers.authorization;
    if (
      await this.authService.canAccessOrganizationAdAccountDetails(
        organizationId,
        authorization,
      )
    ) {
      return this.adAccountService.getOrganizationAdAccountsListForHealthDashboard(
        organizationId,
        userId,
        searchParams,
        paginationOptions,
      );
    } else if (
      await this.authService.canAccessWorkspaceAdAccountDetails(
        workspaceId,
        authorization,
      )
    ) {
      return this.adAccountService.getWorkspaceAdAccountsListForHealthDashboard(
        organizationId,
        workspaceId,
        userId,
        searchParams,
        paginationOptions,
      );
    } else {
      this.logger.error(
        `User - ${userId} doesn't have permissions to access this endpoint. ` +
          `OrganizationId - ${organizationId}, workspaceId - ${workspaceId}.`,
      );
      throw new UnauthorizedException(
        `User doesn't have permissions to access this endpoint.`,
      );
    }
  }

  /**
   * POST a bulk map between ad account and brands
   * @param req - The request object with the ad accounts and brands.
   * @param organizationId - The organization ID represents the unique identifier of the organization to which all workspaces belong.
   * @param workspaceId - The workspace ID represents the unique identifier of the workspace.
   */
  @VmApiOkResponse({
    type: ReadAdAccountMapDto,
  })
  @ApiParam({
    name: 'organizationId',
    description: 'The id of the organization',
  })
  @ApiParam({
    name: 'workspaceId',
    description: 'The id of the workspace',
  })
  @Post(':workspaceId/ad-accounts/brands')
  async createPlatformAdAccountsAndBrandsMaps(
    @Param('organizationId') organizationId: string,
    @Param('workspaceId') workspaceId: number,
    @Request() req: any,
    @Body() adAccountBrandMapDto: CreateAdAccountBrandMapDto,
  ) {
    const { userId } = req;
    const authorization = req.headers.authorization;
    const canAccessEndpoint =
      await this.authService.canAccessOrganizationOrWorkspaceAdAccountMetadata(
        organizationId,
        workspaceId,
        authorization,
      );

    if (canAccessEndpoint) {
      return await this.adAccountService.createAdAccountBrandMap(
        organizationId,
        workspaceId,
        adAccountBrandMapDto,
      );
    }

    this.logger.error(
      `User - ${userId} doesn't have permissions to access this endpoint. ` +
        `OrganizationId - ${organizationId}, workspaceId - ${workspaceId}.`,
    );
    throw new UnauthorizedException(
      `User doesn't have permissions to access this endpoint.`,
    );
  }

  /**
   * POST a bulk map between ad account and markets
   * @param req - The request object with the ad accounts and brands.
   * @param organizationId - The organization ID represents the unique identifier of the organization to which all workspaces belong.
   * @param workspaceId - The workspace ID represents the unique identifier of the workspace.
   */
  @ApiParam({
    name: 'organizationId',
    description: 'The id of the organization',
  })
  @ApiParam({
    name: 'workspaceId',
    description: 'The id of the workspace',
  })
  @Post(':workspaceId/ad-accounts/markets')
  async createPlatformAdAccountsAndMarketsMaps(
    @Param('organizationId') organizationId: string,
    @Param('workspaceId') workspaceId: number,
    @Request() req: any,
    @Body() adAccountMarketMapDto: CreateAdAccountMarketMapDto,
  ) {
    const { userId } = req;
    const authorization = req.headers.authorization;
    const canAccessEndpoint =
      await this.authService.canAccessOrganizationOrWorkspaceAdAccountMetadata(
        organizationId,
        workspaceId,
        authorization,
      );

    if (canAccessEndpoint) {
      return await this.adAccountService.createAdAccountMarketMap(
        organizationId,
        workspaceId,
        adAccountMarketMapDto,
      );
    }

    this.logger.error(
      `User - ${userId} doesn't have permissions to access this endpoint. ` +
        `OrganizationId - ${organizationId}, workspaceId - ${workspaceId}.`,
    );
    throw new UnauthorizedException(
      `User doesn't have permissions to access this endpoint.`,
    );
  }

  /**
   * GET a list of brands associated to ad account
   * @param req - Request attributes
   * @param organizationId - The organization ID represents the unique identifier of the organization to which all workspaces belong.
   * @param workspaceId - The workspace ID represents the unique identifier of the workspace.
   * @param adAccountId - The Ad Account Id that represents the unique identifier of the ad account
   */
  @VmApiOkResponse({
    type: ReadAdAccountBrandsDto,
  })
  @ApiParam({
    name: 'organizationId',
    description: 'The id of the organization',
  })
  @ApiParam({
    name: 'workspaceId',
    description: 'The id of the workspace',
  })
  @ApiParam({
    name: 'adAccountId',
    description: 'The ad account id',
  })
  @Get(':workspaceId/ad-account/:adAccountId/brands')
  async getPlatformAdAccountBrands(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Param('workspaceId') workspaceId: number,
    @Param('adAccountId') adAccountId: string,
  ) {
    const { userId } = req;
    const authorization = req.headers.authorization;
    const canAccessEndpoint =
      await this.authService.canAccessOrganizationOrWorkspaceAdAccountDetails(
        organizationId,
        workspaceId,
        authorization,
      );

    if (canAccessEndpoint) {
      return await this.adAccountService.getPlatformAdAccountBrands(
        organizationId,
        workspaceId,
        adAccountId,
      );
    }

    this.logger.error(
      `User - ${userId} doesn't have permissions to access this endpoint. ` +
        `OrganizationId - ${organizationId}, workspaceId - ${workspaceId}.`,
    );
    throw new UnauthorizedException(
      `User doesn't have permissions to access this endpoint.`,
    );
  }

  /**
   * GET a list of markets associated to ad account
   * @param req - Request attributes
   * @param organizationId - The organization ID represents the unique identifier of the organization to which all workspaces belong.
   * @param workspaceId - The workspace ID represents the unique identifier of the workspace.
   * @param adAccountId - The Ad Account Id that represents the unique identifier of the ad account
   */
  @ApiParam({
    name: 'organizationId',
    description: 'The id of the organization',
  })
  @ApiParam({
    name: 'workspaceId',
    description: 'The id of the workspace',
  })
  @ApiParam({
    name: 'adAccountId',
    description: 'The ad account id',
  })
  @Get(':workspaceId/ad-account/:adAccountId/markets')
  async getPlatformAdAccountMarkets(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Param('workspaceId') workspaceId: number,
    @Param('adAccountId') adAccountId: string,
  ) {
    const { userId } = req;
    const authorization = req.headers.authorization;
    const canAccessEndpoint =
      await this.authService.canAccessOrganizationOrWorkspaceAdAccountDetails(
        organizationId,
        workspaceId,
        authorization,
      );

    if (canAccessEndpoint) {
      return await this.adAccountService.getPlatformAdAccountMarkets(
        organizationId,
        workspaceId,
        adAccountId,
      );
    }

    this.logger.error(
      `User - ${userId} doesn't have permissions to access this endpoint. ` +
        `OrganizationId - ${organizationId}, workspaceId - ${workspaceId}.`,
    );
    throw new UnauthorizedException(
      `User doesn't have permissions to access this endpoint.`,
    );
  }
}
